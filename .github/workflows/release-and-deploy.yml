name: Release and Deploy

on:
  push:
    tags:
      - "v*" # Trigger on version tags

permissions:
  contents: write
  deployments: write

jobs:
  create-release:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # Fetch all history for proper changelog generation

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "18"

      - name: Extract version from tag
        id: version
        run: echo "VERSION=${GITHUB_REF#refs/tags/}" >> $GITHUB_OUTPUT

      - name: Get previous tag
        id: previous_tag
        run: |
          # Get the previous tag (excluding the current one)
          PREVIOUS_TAG=$(git tag --sort=-version:refname | grep -v "${{ steps.version.outputs.VERSION }}" | head -n 1)

          # If no previous tag exists, use the initial commit
          if [ -z "$PREVIOUS_TAG" ]; then
            PREVIOUS_TAG=$(git rev-list --max-parents=0 HEAD)
          fi

          echo "tag=$PREVIOUS_TAG" >> $GITHUB_OUTPUT

      - name: Extract changelog content
        id: changelog
        run: |
          # Extract the changelog content for this version
          VERSION="${{ steps.version.outputs.VERSION }}"
          echo "Extracting changelog for version: $VERSION"

          # Use the changelog extraction script and save to file
          node scripts/extract-changelog.js "$VERSION" > changelog_content.txt

          # Read the content and escape properly for GitHub Actions
          CHANGELOG_CONTENT=$(cat changelog_content.txt)

          # Use GitHub Actions multiline output
          {
            echo "changelog_content<<EOF"
            echo "$CHANGELOG_CONTENT"
            echo "EOF"
          } >> $GITHUB_OUTPUT

      - name: Create GitHub Release
        uses: actions/github-script@v7
        env:
          VERSION: ${{ steps.version.outputs.VERSION }}
          CHANGELOG_CONTENT: ${{ steps.changelog.outputs.changelog_content }}
          PREVIOUS_TAG: ${{ steps.previous_tag.outputs.tag }}
          REPOSITORY: ${{ github.repository }}
        with:
          script: |
            const { VERSION, CHANGELOG_CONTENT, PREVIOUS_TAG, REPOSITORY } = process.env;

            const release = await github.rest.repos.createRelease({
              owner: context.repo.owner,
              repo: context.repo.repo,
              tag_name: VERSION,
              name: `Release ${VERSION}`,
              body: `${CHANGELOG_CONTENT}\n\n---\n\n**Full Changelog**: https://github.com/${REPOSITORY}/compare/${PREVIOUS_TAG}..${VERSION}`,
              draft: false,
              prerelease: false
            });

            console.log('GitHub release created successfully');

  deploy-production:
    runs-on: ubuntu-latest
    needs: create-release
    steps:
      - uses: actions/checkout@v4
      - name: Deploy to Vercel
        id: deploy
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          vercel-args: "--prod"
      - name: Create GitHub Deployment
        uses: actions/github-script@v7
        env:
          DEPLOYMENT_URL: ${{ steps.deploy.outputs.url }}
        with:
          script: |
            const { DEPLOYMENT_URL } = process.env;

            const deployment = await github.rest.repos.createDeployment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              ref: context.ref,
              environment: 'production',
              description: 'Production deployment via Vercel',
              auto_merge: false,
              required_contexts: []
            });

            await github.rest.repos.createDeploymentStatus({
              owner: context.repo.owner,
              repo: context.repo.repo,
              deployment_id: deployment.data.id,
              state: 'success',
              environment_url: DEPLOYMENT_URL,
              description: 'Deployment successful'
            });

            console.log('GitHub deployment created successfully');
