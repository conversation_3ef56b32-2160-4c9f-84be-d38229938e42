/* Import color variables */
@import "../styles/variables.css";

/* Custom slider styles */
.font-size-slider {
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  cursor: pointer;
}

/* Webkit browsers (Chrome, Safari, Edge) */
.font-size-slider::-webkit-slider-track {
  background: transparent;
  height: 8px;
  border-radius: 4px;
}

.font-size-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  height: 18px;
  width: 18px;
  border-radius: 50%;
  background: var(--color-primary-500);
  cursor: pointer;
  border: 2px solid var(--color-white);
  box-shadow: 0 2px 4px var(--color-shadow-light);
  transition: all 0.2s ease;
}

.font-size-slider::-webkit-slider-thumb:hover {
  background: var(--color-primary-600);
  box-shadow: 0 2px 8px var(--color-shadow-medium);
  transform: scale(1.1);
}

.font-size-slider:focus::-webkit-slider-thumb {
  box-shadow: 0 0 0 3px var(--color-focus-blue);
}

/* Firefox */
.font-size-slider::-moz-range-track {
  background: transparent;
  height: 8px;
  border-radius: 4px;
  border: none;
}

.font-size-slider::-moz-range-thumb {
  height: 18px;
  width: 18px;
  border-radius: 50%;
  background: var(--color-primary-500);
  cursor: pointer;
  border: 2px solid var(--color-white);
  box-shadow: 0 2px 4px var(--color-shadow-light);
  transition: all 0.2s ease;
}

.font-size-slider::-moz-range-thumb:hover {
  background: var(--color-primary-600);
  box-shadow: 0 2px 8px var(--color-shadow-medium);
  transform: scale(1.1);
}

/* Dark mode styles */
.dark .font-size-slider::-webkit-slider-thumb {
  border: 2px solid var(--color-gray-700);
}

.dark .font-size-slider::-moz-range-thumb {
  border: 2px solid var(--color-gray-700);
}
