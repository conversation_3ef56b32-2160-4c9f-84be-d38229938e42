{"name": "mdzen", "private": true, "version": "1.0.2", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "release": "./scripts/release.sh", "release:patch": "./scripts/release.sh patch", "release:minor": "./scripts/release.sh minor", "release:major": "./scripts/release.sh major", "changelog": "node scripts/extract-changelog.js"}, "dependencies": {"@codemirror/autocomplete": "^6.18.6", "@codemirror/commands": "^6.8.1", "@codemirror/lang-markdown": "^6.3.3", "@codemirror/language": "^6.11.2", "@codemirror/lint": "^6.8.5", "@codemirror/search": "^6.5.11", "@codemirror/state": "^6.5.2", "@codemirror/theme-one-dark": "^6.1.3", "@codemirror/view": "^6.38.1", "codemirror": "^6.0.2", "dompurify": "^3.2.6", "html2pdf.js": "^0.10.3", "marked": "^16.1.1", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@eslint/js": "^9.30.1", "@tailwindcss/typography": "^0.5.16", "@types/dompurify": "^3.2.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}, "packageManager": "pnpm@10.12.4"}